/// City Validation Service
///
/// Service for validating if a user's city is supported by the application
/// Checks against Firestore app_settings collection supported_cities document
library city_validation_service;

import 'package:flutter/foundation.dart';
import 'package:towasl/shared/services/firebase_service.dart';

/// City Validation Service Interface
///
/// Defines the contract for city validation operations
/// Allows for easy mocking in tests
abstract class CityValidationService {
  /// Check if a city is supported
  ///
  /// Validates if the given city is in the list of supported cities
  ///
  /// @param city The city name to validate
  /// @return A Future that resolves to true if city is supported, false otherwise
  Future<bool> isCitySupported(String city);

  /// Get list of supported cities
  ///
  /// Retrieves the complete list of supported cities from Firestore
  ///
  /// @return A Future that resolves to a list of supported city names
  Future<List<String>> getSupportedCities();
}

/// City Validation Service Implementation
///
/// Implements city validation using Firebase Firestore
/// Uses new structure: countries/{countryId}/cities subcollection
/// Supports Saudi Arabia (SA) country with cities subcollection
class CityValidationServiceImpl implements CityValidationService {
  /// Firebase service for Firestore operations
  final FirebaseService _firebaseService;

  /// Parent collection name in Firestore
  static const String _parentCollectionName = 'countries';

  /// Country document ID for Saudi Arabia
  static const String _countryId = 'SA';

  /// Subcollection name for cities
  static const String _citiesSubcollection = 'cities';

  /// Creates a CityValidationServiceImpl
  ///
  /// @param firebaseService Firebase service instance
  const CityValidationServiceImpl(this._firebaseService);

  @override
  Future<bool> isCitySupported(String city) async {
    try {
      if (city.isEmpty) {
        if (kDebugMode) {
          print('CityValidationService: Empty city provided');
        }
        return false;
      }

      final supportedCities = await getSupportedCities();

      // Case-insensitive comparison
      final normalizedCity = city.toLowerCase().trim();
      final isSupported = supportedCities
          .map((c) => c.toLowerCase().trim())
          .contains(normalizedCity);

      if (kDebugMode) {
        print(
            'CityValidationService: City "$city" is ${isSupported ? 'supported' : 'not supported'}');
      }

      return isSupported;
    } catch (e) {
      if (kDebugMode) {
        print('CityValidationService: Error checking city support - $e');
      }
      // Return false on error to be safe
      return false;
    }
  }

  @override
  Future<List<String>> getSupportedCities() async {
    try {
      if (kDebugMode) {
        print(
            'CityValidationService: Fetching supported cities from Firestore');
      }

      // Get all city documents from countries/SA/cities subcollection
      final querySnapshot = await _firebaseService.getSubcollectionDocuments(
        _parentCollectionName,
        _countryId,
        _citiesSubcollection,
      );

      final cities = <String>[];

      for (final doc in querySnapshot.docs) {
        if (doc.exists && doc.data() != null) {
          final data = doc.data() as Map<String, dynamic>;

          // Add Arabic name if available
          if (data.containsKey('name_ar') &&
              data['name_ar'] != null &&
              data['name_ar'].toString().isNotEmpty) {
            cities.add(data['name_ar'].toString());
          }

          // Add English name if available
          if (data.containsKey('name_en') &&
              data['name_en'] != null &&
              data['name_en'].toString().isNotEmpty) {
            cities.add(data['name_en'].toString());
          }
        }
      }

      if (kDebugMode) {
        print(
            'CityValidationService: Found ${cities.length} supported cities from subcollection structure');
      }

      return cities;
    } catch (e) {
      if (kDebugMode) {
        print('CityValidationService: Error fetching supported cities - $e');
      }

      // Return empty list on error
      return [];
    }
  }
}

/// City Validation Result
///
/// Represents the result of a city validation check
class CityValidationResult {
  /// Whether the city is supported
  final bool isSupported;

  /// Message explaining the validation result
  final String message;

  /// Creates a CityValidationResult
  ///
  /// @param isSupported Whether the city is supported
  /// @param message Explanation message
  const CityValidationResult({
    required this.isSupported,
    required this.message,
  });

  /// Creates a successful validation result
  ///
  /// @param city The supported city name
  /// @return CityValidationResult indicating success
  factory CityValidationResult.supported(String city) {
    return CityValidationResult(
      isSupported: true,
      message: 'City "$city" is supported',
    );
  }

  /// Creates a failed validation result
  ///
  /// @param city The unsupported city name
  /// @return CityValidationResult indicating failure
  factory CityValidationResult.unsupported(String city) {
    return const CityValidationResult(
      isSupported: false,
      message:
          'The region is not supported now, we will let you know once it is supported.',
    );
  }

  /// Creates an error validation result
  ///
  /// @param error The error message
  /// @return CityValidationResult indicating error
  factory CityValidationResult.error(String error) {
    return CityValidationResult(
      isSupported: false,
      message: 'Unable to validate city: $error',
    );
  }
}
