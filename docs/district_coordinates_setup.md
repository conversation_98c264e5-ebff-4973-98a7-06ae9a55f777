# District Coordinates Setup

This document explains how to set up district coordinates in Firestore and how the app handles them.

## Overview

The app now supports GeoPoint coordinates for districts stored in Firestore. When users capture their location via GPS or manually select a district, the app will use the district's coordinates if available. The app uses Firebase's GeoPoint data type for efficient coordinate storage and querying.

## Firestore Structure

Districts are now stored in the `countries/{countryId}/cities/{cityId}` document structure with the following format:

### New Structure: countries/{countryId}/cities/{cityId}

**Collection:** `countries`
**Document:** `SA` (for Saudi Arabia)
**Subcollection:** `cities`

Each city document (e.g., `riyadh`, `jeddah`) contains:

```json
{
  "name_ar": "الرياض",
  "name_en": "Riyadh",
  "districts": {
    "olaya": {
      "name_ar": "العليا",
      "name_en": "Olaya",
      "location": [lat,lng] (geopoint)
    },
    "malaz": {
      "name_ar": "الملز",
      "name_en": "Malaz",
      "location": [lat,lng] (geopoint)
    },
    "arqa": {
      "name_ar": "عرقة",
      "name_en": "Arqa",
      "location": [lat,lng] (geopoint)
    }
  }
}
```

**Path Examples:**
- `countries/SA/cities/riyadh`
- `countries/SA/cities/jeddah`
- `countries/SA/cities/dammam`

## Required Fields

For each district, the following fields are required:
- `name_ar`: Arabic name of the district
- `name_en`: English name of the district

Optional coordinate field:
- `location`: GeoPoint containing latitude and longitude coordinates

### Legacy Support

For backward compatibility, the app also supports legacy coordinate fields:
- `lat`: Latitude coordinate (double or string)
- `lng`: Longitude coordinate (double or string)

**Note**: New implementations should use the `location` GeoPoint field. Legacy `lat`/`lng` fields will be automatically converted to GeoPoint internally.

## How It Works

### 1. GPS Location Capture
When a user captures their location via GPS:
1. The app gets GPS coordinates and address information
2. It attempts to match the GPS-detected district name with districts in Firestore
3. If a match is found and the district has coordinates, the app updates the location to use the district's coordinates
4. If no match is found or the district has no coordinates, the app keeps the GPS coordinates

### 2. Manual District Selection
When a user manually selects a district:
1. If the selected district has coordinates, the app updates the location to use those coordinates
2. If the district has no coordinates, the app keeps the current GPS coordinates and only updates the district name

### 3. Location Storage
When the location is saved:
- The current coordinates (either GPS or district coordinates) are stored in `user_location.location` as a GeoPoint
- The district name is stored in `user_location.district`
- All location data is saved to the user's document in Firestore

### 4. Data Migration
The app automatically handles migration from legacy `lat`/`lng` fields to the new GeoPoint structure:
- When reading data, it first checks for the `location` GeoPoint field
- If not found, it falls back to legacy `lat`/`lng` fields and converts them to GeoPoint
- When writing data, it always uses the new GeoPoint structure

## Code Changes Made

### 1. District Model Updates
- Updated `District` class to use `location` GeoPoint field instead of separate `latitude`/`longitude` fields
- Added convenience constructor `District.withCoordinates()` for backward compatibility
- Added `latitude` and `longitude` getters that extract values from GeoPoint
- Updated `hasValidCoordinates` getter to work with GeoPoint
- Updated `coordinatesString` getter for debugging

### 2. District Service Updates
- Updated `getDistrictsForCity` to parse GeoPoint from Firestore with fallback to legacy lat/lng
- Added support for both GeoPoint and legacy coordinate formats
- Enhanced coordinate parsing to handle both number and string types
- Added `findDistrictByName` method to search for districts by name
- Added support for both exact and partial name matching

### 3. Location Provider Updates
- Updated `selectDistrict` to use district coordinates when available
- Added `_matchGpsDistrictWithFirestore` method to automatically match GPS districts
- Enhanced logging to show coordinate updates

### 4. Location Entity Updates
- Updated `LocationEntity` class to use `location` GeoPoint field
- Added convenience constructor `LocationEntity.withCoordinates()` for backward compatibility
- Added `latitude` and `longitude` getters that extract values from GeoPoint
- Updated all coordinate-related methods to work with GeoPoint

### 5. User Location Model Updates
- Updated `UserLocation` class to use `location` GeoPoint field
- Added convenience constructor `UserLocation.withCoordinates()` for backward compatibility
- Updated JSON serialization to use GeoPoint with fallback for legacy data
- Added `lat` and `lng` getters for backward compatibility

### 6. Location Service Updates
- Updated `LocationData` class to use `location` GeoPoint field
- Added convenience constructor `LocationData.withCoordinates()` for backward compatibility
- Updated all location creation methods to use GeoPoint

## Benefits

1. **Efficient Storage**: GeoPoint provides optimized storage and querying for geographic coordinates
2. **Accurate Positioning**: Districts now have precise coordinates instead of relying on GPS accuracy
3. **Consistent Location Data**: All users selecting the same district will have the same coordinates
4. **Backward Compatibility**: Legacy lat/lng data is automatically converted to GeoPoint
5. **Automatic Matching**: GPS-captured districts are automatically matched with Firestore districts
6. **Future-Ready**: GeoPoint enables advanced geospatial queries and features

## Setup Instructions

### For New Implementations
1. **Create Firestore Structure**: Set up the new `countries/{countryId}/cities/{cityId}` structure
2. **Add City Documents**: Create documents for each city in `countries/SA/cities/` with `name_ar`, `name_en`, and `districts` fields
3. **Add District Coordinates**: Add `location` GeoPoint fields to districts within each city document
4. **Use GeoPoint Constructor**: Create GeoPoint objects with `GeoPoint(latitude, longitude)`
5. **Test GPS Matching**: Capture location via GPS and verify district matching works
6. **Test Manual Selection**: Manually select districts and verify coordinates are updated
7. **Verify Storage**: Check that user locations are saved with correct GeoPoint coordinates

### For Existing Implementations (Migration Required)
1. **Data Migration Required**: The old `app_settings/supported_cities` structure is no longer supported
2. **Create New Structure**: Set up the new `countries/{countryId}/cities/{cityId}` structure
3. **Migrate Data**: Move existing city and district data to the new structure
4. **Update References**: Ensure all code references use the new structure
5. **Test Thoroughly**: Verify that all location and district functionality works with the new system

## Troubleshooting

### District Not Found
- Check that the district name in Firestore matches the GPS-detected name
- The matching is case-insensitive and supports partial matches
- Check debug logs for district matching attempts

### Coordinates Not Updated
- Verify that the district has valid `location` GeoPoint or legacy `lat`/`lng` fields in Firestore
- Check that coordinates are not 0.0 (which is considered invalid)
- Review debug logs for coordinate update messages

### GPS vs District Coordinates
- GPS coordinates are used when no district match is found
- District coordinates override GPS coordinates when available
- Users can still manually select districts even if GPS matching fails

## Future Enhancements

- **Geospatial Queries**: Leverage GeoPoint for advanced location-based queries (nearby districts, distance calculations)
- **Coordinate Validation**: Add validation to ensure coordinates are within expected geographic bounds
- **Caching**: Implement caching for district data to improve performance
- **Multiple Coordinate Systems**: Add support for different coordinate systems if needed
- **District Boundaries**: Consider adding district boundaries for more precise location detection
- **Geofencing**: Use GeoPoint data for geofencing and location-based notifications
